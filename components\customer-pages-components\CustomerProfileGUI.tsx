import React, { useState, useEffect } from 'react';
import { Card, YStack, Text, View, H2, XStack, Paragraph, Button, Input, Label, Sheet, H4, Separator, Switch } from 'tamagui';
import { Avatar, AvatarImage, AvatarFallback } from '@tamagui/avatar'
import { Ionicons } from '@expo/vector-icons'
import { ImageBackground, Pressable, View as RNView, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useCurrentUserData } from '../useCurrentUserData';
import { apiService } from '../../services/apiService';

// Define types locally since they're not exported from apiService
interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  country?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  notifications?: boolean;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}
import { LanguageSwitcher } from '../common/LanguageSwitcher';
import { useLanguageStore } from '../../stores/languageStore';

type CustomerProfileGUIProps = {
  title: string;
};

export const CustomerProfileGUI = ({ title }: CustomerProfileGUIProps) => {
    const { t } = useTranslation();
    const { currentLanguage, isRTL } = useLanguageStore();
    const { user, isLoading, loadUserProfile, initializeUser } = useCurrentUserData();
    const [editMode, setEditMode] = useState<'profile' | 'password' | null>(null);
    const [updating, setUpdating] = useState(false);

    // Profile edit form state
    const [profileForm, setProfileForm] = useState<UpdateProfileRequest>({});

    // Password change form state
    const [passwordForm, setPasswordForm] = useState<ChangePasswordRequest>({
        currentPassword: '',
        newPassword: ''
    });

    const AVATAR_SIZE = 128;
    const CUTOUT_RADIUS = AVATAR_SIZE / 2 + 5;

    // Load user profile on component mount
    useEffect(() => {
        const initializeUserData = async () => {
            console.log('🔄 Profile page: Initializing user...');
            if (!user) {
                console.log('🔄 Profile page: User not found, initializing...');
                try {
                    await initializeUser();
                } catch (error) {
                    console.log('❌ Profile page: Failed to initialize user:', error);
                }
            } else {
                console.log('✅ Profile page: User already loaded:', user.email);
            }
        };
        initializeUserData();
    }, [user, initializeUser]);

    // Initialize form with user data when user loads
    useEffect(() => {
        if (user) {
            setProfileForm({
                firstName: user.firstName || '',
                lastName: user.lastName || '',
                phoneNumber: user.phoneNumber || '',
                address: user.address || '',
                city: user.city || '',
                country: user.country || '',
                dateOfBirth: user.dateOfBirth || '',
                gender: user.gender || undefined
            });
        }
    }, [user]);

    // Reset form to original user data
    const resetProfileForm = () => {
        if (user) {
            setProfileForm({
                firstName: user.firstName || '',
                lastName: user.lastName || '',
                phoneNumber: user.phoneNumber || '',
                address: user.address || '',
                city: user.city || '',
                country: user.country || '',
                dateOfBirth: user.dateOfBirth || '',
                gender: user.gender || undefined
            });
        }
    };

    const handleUpdateProfile = async () => {
        if (!user) return;

        // Basic validation
        if (!profileForm.firstName?.trim() || !profileForm.lastName?.trim()) {
            Alert.alert('Error', 'First name and last name are required');
            return;
        }

        if (!profileForm.phoneNumber?.trim()) {
            Alert.alert('Error', 'Phone number is required');
            return;
        }

        setUpdating(true);
        try {
            const response = await apiService.updateProfile(profileForm);

            if (response.success) {
                Alert.alert('Success', 'Profile updated successfully');
                await loadUserProfile(); // Reload user data
                setEditMode(null);
                // Reset form to updated values
                setProfileForm({
                    firstName: response.data?.user?.firstName || profileForm.firstName,
                    lastName: response.data?.user?.lastName || profileForm.lastName,
                    phoneNumber: response.data?.user?.phoneNumber || profileForm.phoneNumber,
                    address: response.data?.user?.address || profileForm.address,
                    city: response.data?.user?.city || profileForm.city,
                    country: response.data?.user?.country || profileForm.country,
                    dateOfBirth: response.data?.user?.dateOfBirth || profileForm.dateOfBirth,
                    gender: response.data?.user?.gender || profileForm.gender
                });
            } else {
                Alert.alert('Error', response.message || 'Failed to update profile');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to update profile. Please check your connection and try again.');
            console.error('Profile update error:', error);
        } finally {
            setUpdating(false);
        }
    };

    const handleChangePassword = async () => {
        if (!passwordForm.currentPassword || !passwordForm.newPassword) {
            Alert.alert('Error', 'Please fill in all password fields');
            return;
        }

        if (passwordForm.newPassword.length < 6) {
            Alert.alert('Error', 'New password must be at least 6 characters long');
            return;
        }

        setUpdating(true);
        try {
            const response = await apiService.changePassword(
                passwordForm.currentPassword,
                passwordForm.newPassword
            );

            if (response.success) {
                Alert.alert('Success', 'Password changed successfully');
                setPasswordForm({ currentPassword: '', newPassword: '' });
                setEditMode(null);
            } else {
                Alert.alert('Error', response.message || 'Failed to change password');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to change password');
            console.error('Password change error:', error);
        } finally {
            setUpdating(false);
        }
    };

    const handleLogout = async () => {
        Alert.alert(
            'Logout',
            'Are you sure you want to logout?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Logout',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await apiService.logout();
                            router.replace('/');
                        } catch (error) {
                            console.error('Logout error:', error);
                            router.replace('/');
                        }
                    }
                }
            ]
        );
    };

    if (isLoading) {
        return (
            <YStack flex={1} alignItems="center" justifyContent="center" backgroundColor="#fff">
                <ActivityIndicator size="large" color="#67B329" />
                <Text marginTop="$4" color="$gray9">{t('common.loading', { defaultValue: 'Loading...' })}</Text>
            </YStack>
        );
    }

    if (!user) {
        return (
            <YStack flex={1} alignItems="center" justifyContent="center" backgroundColor="#fff">
                <Ionicons name="person-circle-outline" size={64} color="#ccc" />
                <Text marginTop="$4" color="$gray9">{t('profile.failedToLoad', { defaultValue: 'Failed to load profile' })}</Text>
                <Button
                    marginTop="$4"
                    onPress={loadUserProfile}
                    backgroundColor="#67B329"
                >
                    {t('common.retry', { defaultValue: 'Retry' })}
                </Button>
            </YStack>
        );
    }

    return (
        <YStack flex={1} position="relative" backgroundColor="#fff" width={'100%'} height={'100%'}>
            <YStack position="absolute" top={50} zIndex={1} width="100%" alignItems="center" justifyContent='center'>
                <H2 style={{color: 'white', fontWeight: 'bold'}}>{t('navigation.profile', { defaultValue: 'Profile' })}</H2>
            </YStack>

            {/* Background Header */}
            <RNView
                style={{
                height: 250,
                width: '100%',
                borderBottomLeftRadius: 20,
                borderBottomRightRadius: 20,
                overflow: 'hidden',
                position: 'absolute',
                top: 0,
                }}
            >
                <ImageBackground
                    source={require('../../assets/profile-bg.png')}
                    resizeMode="cover"
                    style={{
                    height: 250,
                    width: '100%',
                    position: 'absolute',
                    top: 0,
                    zIndex: 0,
                    justifyContent: 'flex-end',
                    alignItems: 'center'
                    }}
                >
                    <View
                    width={CUTOUT_RADIUS * 2}
                    height={CUTOUT_RADIUS * 2}
                    borderRadius={CUTOUT_RADIUS}
                    backgroundColor="#fff"
                    position="absolute"
                    bottom={-CUTOUT_RADIUS}
                    />
                </ImageBackground>
            </RNView>

            {/* Avatar and User Info */}
            <YStack alignItems="center" marginTop={185} zIndex={3}>
                <View position="relative" width={AVATAR_SIZE} height={AVATAR_SIZE}>
                    <Avatar circular size={AVATAR_SIZE} borderWidth={3} borderColor="#67B329">
                        <AvatarImage src={`https://ui-avatars.com/api/?name=${user.firstName}+${user.lastName}&background=67B329&color=fff&size=128`} />
                        <AvatarFallback backgroundColor="$gray6" alignItems="center" justifyContent="center">
                            <Ionicons name="person" size={64} color="#FFFFFF" />
                        </AvatarFallback>
                    </Avatar>
                    <Pressable
                        onPress={() => {
                            console.log('Camera button pressed');
                            // TODO: Implement image picker
                        }}
                        style={({ pressed }) => ({
                            position: 'absolute',
                            bottom: 5,
                            right: 5,
                            backgroundColor: pressed ? '#5A9E1F' : '#67B329',
                            borderRadius: 20,
                            width: 40,
                            height: 40,
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderWidth: 2,
                            borderColor: '#fff',
                            shadowColor: '#000',
                            shadowOffset: { width: 0, height: 2 },
                            shadowOpacity: 0.25,
                            shadowRadius: 3.84,
                            elevation: 5,
                        })}
                    >
                        <Ionicons name="camera" size={20} color="#fff" />
                    </Pressable>
                </View>

                <Text fontSize="$7" fontWeight="700" marginTop="$4">
                    {user.firstName} {user.lastName}
                </Text>
                <Text color='$gray9' padding={'$1'}>@{user.username}</Text>
                <Text color='$gray9' padding={'$1'}>{user.email}</Text>
                <Text fontSize="$5" fontWeight="600" padding={'$1'}>{user.phoneNumber}</Text>

                <View
                    height={4}
                    width="80%"
                    backgroundColor="#67B329"
                    borderRadius={9999}
                    alignSelf="center"
                    marginVertical="$4"
                />
            </YStack>
            {/* Profile Menu Cards */}
            <ScrollView
                contentContainerStyle={{
                    flexGrow: 1,
                    width: '95%',
                    paddingBottom: 110,
                    alignContent: 'center',
                    alignSelf: 'center',
                    rowGap: 5
                }}
                showsVerticalScrollIndicator={false}
                style={{
                    borderBottomColor: '#67B329',
                    borderBottomWidth: 3,
                    borderCurve: 'circular',
                    borderRadius: 10
                }}
            >
                {/* Profile Information Card */}
                <Card
                    backgroundColor="#fff"
                    borderRadius={15}
                    padding="$4"
                    marginVertical="$2"
                    shadowColor="#000"
                    shadowOffset={{ width: 0, height: 2 }}
                    shadowOpacity={0.1}
                    shadowRadius={4}
                    elevation={3}
                    borderWidth={1}
                    borderColor="$gray4"
                >
                    <XStack alignItems="center" justifyContent="space-between">
                        <XStack alignItems="center" gap="$3">
                            <View
                                backgroundColor="#67B329"
                                borderRadius={25}
                                width={50}
                                height={50}
                                alignItems="center"
                                justifyContent="center"
                            >
                                <Ionicons name="person" size={24} color="#fff" />
                            </View>
                            <YStack>
                                <Text fontSize="$5" fontWeight="600" color="$gray12">
                                    {t('profile.editProfile', { defaultValue: 'Edit Profile' })}
                                </Text>
                                <Text fontSize="$3" color="$gray9">
                                    {t('profile.updatePersonalInfo', { defaultValue: 'Update your personal information' })}
                                </Text>
                            </YStack>
                        </XStack>
                        <Pressable
                            onPress={() => setEditMode('profile')}
                            style={({ pressed }) => ({
                                backgroundColor: pressed ? '#f0f0f0' : 'transparent',
                                borderRadius: 20,
                                padding: 8,
                            })}
                        >
                            <Ionicons name="chevron-forward" size={20} color="#67B329" />
                        </Pressable>
                    </XStack>
                </Card>

                {/* Address Information Display */}
                <Card
                    backgroundColor="#fff"
                    borderRadius={15}
                    padding="$4"
                    marginVertical="$2"
                    shadowColor="#000"
                    shadowOffset={{ width: 0, height: 2 }}
                    shadowOpacity={0.1}
                    shadowRadius={4}
                    elevation={3}
                    borderWidth={1}
                    borderColor="$gray4"
                >
                    <XStack alignItems="center" gap="$3">
                        <View
                            backgroundColor="#67B329"
                            borderRadius={25}
                            width={50}
                            height={50}
                            alignItems="center"
                            justifyContent="center"
                        >
                            <Ionicons name="location" size={24} color="#fff" />
                        </View>
                        <YStack flex={1}>
                            <Text fontSize="$5" fontWeight="600" color="$gray12">
                                {t('auth.address', { defaultValue: 'Address' })}
                            </Text>
                            <Text fontSize="$3" color="$gray9" numberOfLines={2}>
                                {user.address}, {user.city}, {user.country}
                            </Text>
                        </YStack>
                    </XStack>
                </Card>

                {/* Change Password Card */}
                <Card
                    backgroundColor="#fff"
                    borderRadius={15}
                    padding="$4"
                    marginVertical="$2"
                    shadowColor="#000"
                    shadowOffset={{ width: 0, height: 2 }}
                    shadowOpacity={0.1}
                    shadowRadius={4}
                    elevation={3}
                    borderWidth={1}
                    borderColor="$gray4"
                >
                    <XStack alignItems="center" justifyContent="space-between">
                        <XStack alignItems="center" gap="$3">
                            <View
                                backgroundColor="#67B329"
                                borderRadius={25}
                                width={50}
                                height={50}
                                alignItems="center"
                                justifyContent="center"
                            >
                                <Ionicons name="lock-closed" size={24} color="#fff" />
                            </View>
                            <YStack>
                                <Text fontSize="$5" fontWeight="600" color="$gray12">
                                    {t('auth.changePassword', { defaultValue: 'Change Password' })}
                                </Text>
                                <Text fontSize="$3" color="$gray9">
                                    {t('profile.updatePassword', { defaultValue: 'Update your password' })}
                                </Text>
                            </YStack>
                        </XStack>
                        <Pressable
                            onPress={() => setEditMode('password')}
                            style={({ pressed }) => ({
                                backgroundColor: pressed ? '#f0f0f0' : 'transparent',
                                borderRadius: 20,
                                padding: 8,
                            })}
                        >
                            <Ionicons name="chevron-forward" size={20} color="#67B329" />
                        </Pressable>
                    </XStack>
                </Card>

                {/* Language Settings Card */}
                <Card
                    backgroundColor="#fff"
                    borderRadius={15}
                    padding="$4"
                    marginVertical="$2"
                    shadowColor="#000"
                    shadowOffset={{ width: 0, height: 2 }}
                    shadowOpacity={0.1}
                    shadowRadius={4}
                    elevation={3}
                    borderWidth={1}
                    borderColor="$gray4"
                >
                    <XStack alignItems="center" gap="$3">
                        <View
                            backgroundColor="#67B329"
                            borderRadius={25}
                            width={50}
                            height={50}
                            alignItems="center"
                            justifyContent="center"
                        >
                            <Ionicons name="language" size={24} color="#fff" />
                        </View>
                        <YStack flex={1}>
                            <Text fontSize="$5" fontWeight="600" color="$gray12">
                                {t('profile.language', { defaultValue: 'Language' })}
                            </Text>
                            <Text fontSize="$3" color="$gray9">
                                {currentLanguage === 'en' ? 'English' : 'العربية'}
                            </Text>
                        </YStack>
                    </XStack>

                    <YStack marginTop="$3">
                        <LanguageSwitcher variant="sheet" />
                    </YStack>
                </Card>

                {/* Account Status Card */}
                <Card
                    backgroundColor="#fff"
                    borderRadius={15}
                    padding="$4"
                    marginVertical="$2"
                    shadowColor="#000"
                    shadowOffset={{ width: 0, height: 2 }}
                    shadowOpacity={0.1}
                    shadowRadius={4}
                    elevation={3}
                    borderWidth={1}
                    borderColor="$gray4"
                >
                    <XStack alignItems="center" gap="$3">
                        <View
                            backgroundColor={user.isEmailVerified ? "#67B329" : "#f59e0b"}
                            borderRadius={25}
                            width={50}
                            height={50}
                            alignItems="center"
                            justifyContent="center"
                        >
                            <Ionicons
                                name={user.isEmailVerified ? "checkmark-circle" : "warning"}
                                size={24}
                                color="#fff"
                            />
                        </View>
                        <YStack flex={1}>
                            <Text fontSize="$5" fontWeight="600" color="$gray12">
                                {t('profile.accountStatus', { defaultValue: 'Account Status' })}
                            </Text>
                            <Text fontSize="$3" color={user.isEmailVerified ? "$green9" : "$orange9"}>
                                {user.isEmailVerified ? t('auth.emailVerified', { defaultValue: 'Email Verified' }) : t('auth.emailNotVerified', { defaultValue: 'Email Not Verified' })}
                            </Text>
                        </YStack>
                    </XStack>
                </Card>

                {/* Logout Card */}
                <Card
                    backgroundColor="#fff"
                    borderRadius={15}
                    padding="$4"
                    marginVertical="$2"
                    shadowColor="#000"
                    shadowOffset={{ width: 0, height: 2 }}
                    shadowOpacity={0.1}
                    shadowRadius={4}
                    elevation={3}
                    borderWidth={1}
                    borderColor="$gray4"
                >
                    <XStack alignItems="center" justifyContent="space-between">
                        <XStack alignItems="center" gap="$3">
                            <View
                                backgroundColor="#dc2626"
                                borderRadius={25}
                                width={50}
                                height={50}
                                alignItems="center"
                                justifyContent="center"
                            >
                                <Ionicons name="log-out" size={24} color="#fff" />
                            </View>
                            <YStack>
                                <Text fontSize="$5" fontWeight="600" color="$gray12">
                                    {t('auth.logout', { defaultValue: 'Logout' })}
                                </Text>
                                <Text fontSize="$3" color="$gray9">
                                    {t('profile.signOutAccount', { defaultValue: 'Sign out of your account' })}
                                </Text>
                            </YStack>
                        </XStack>
                        <Pressable
                            onPress={handleLogout}
                            style={({ pressed }) => ({
                                backgroundColor: pressed ? '#f0f0f0' : 'transparent',
                                borderRadius: 20,
                                padding: 8,
                            })}
                        >
                            <Ionicons name="chevron-forward" size={20} color="#dc2626" />
                        </Pressable>
                    </XStack>
                </Card>
            </ScrollView>

            {/* Profile Edit Modal */}
            <Sheet
                modal
                open={editMode === 'profile'}
                onOpenChange={(open: boolean) => !open && setEditMode(null)}
                snapPoints={[90]}
                dismissOnSnapToBottom
            >
                <Sheet.Overlay />
                <Sheet.Handle />
                <Sheet.Frame padding="$4" flex={1}>
                    <YStack flex={1} gap="$4">
                        <YStack gap="$2">
                            <H4>Edit Profile</H4>
                            <Separator />
                        </YStack>

                        <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                            <YStack gap="$3" paddingBottom="$4">
                                <YStack gap="$2">
                                    <Label htmlFor="firstName">{t('auth.firstName', { defaultValue: 'First Name' })}</Label>
                                    <Input
                                        id="firstName"
                                        value={profileForm.firstName || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, firstName: text }))}
                                        placeholder={t('auth.enterFirstName', { defaultValue: 'Enter first name' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="lastName">{t('auth.lastName', { defaultValue: 'Last Name' })}</Label>
                                    <Input
                                        id="lastName"
                                        value={profileForm.lastName || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, lastName: text }))}
                                        placeholder={t('auth.enterLastName', { defaultValue: 'Enter last name' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="phoneNumber">{t('auth.phone', { defaultValue: 'Phone Number' })}</Label>
                                    <Input
                                        id="phoneNumber"
                                        value={profileForm.phoneNumber || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, phoneNumber: text }))}
                                        placeholder={t('auth.enterPhone', { defaultValue: 'Enter phone number' })}
                                        keyboardType="phone-pad"
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="address">{t('auth.address', { defaultValue: 'Address' })}</Label>
                                    <Input
                                        id="address"
                                        value={profileForm.address || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, address: text }))}
                                        placeholder={t('auth.enterAddress', { defaultValue: 'Enter address' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="city">{t('auth.city', { defaultValue: 'City' })}</Label>
                                    <Input
                                        id="city"
                                        value={profileForm.city || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, city: text }))}
                                        placeholder={t('auth.enterCity', { defaultValue: 'Enter city' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="country">{t('auth.country', { defaultValue: 'Country' })}</Label>
                                    <Input
                                        id="country"
                                        value={profileForm.country || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, country: text }))}
                                        placeholder={t('auth.enterCountry', { defaultValue: 'Enter country' })}
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="dateOfBirth">{t('auth.dateOfBirth', { defaultValue: 'Date of Birth' })}</Label>
                                    <Input
                                        id="dateOfBirth"
                                        value={profileForm.dateOfBirth || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, dateOfBirth: text }))}
                                        placeholder="YYYY-MM-DD"
                                    />
                                </YStack>

                                <YStack gap="$2">
                                    <Label htmlFor="gender">{t('auth.gender', { defaultValue: 'Gender' })}</Label>
                                    <Input
                                        id="gender"
                                        value={profileForm.gender || ''}
                                        onChangeText={(text) => setProfileForm(prev => ({ ...prev, gender: text as 'male' | 'female' | 'other' }))}
                                        placeholder={`${t('auth.male', { defaultValue: 'male' })}, ${t('auth.female', { defaultValue: 'female' })}, ${t('auth.other', { defaultValue: 'other' })}`}
                                    />
                                </YStack>
                            </YStack>
                        </ScrollView>

                        <XStack gap="$3" paddingTop="$2">
                            <Button
                                flex={1}
                                variant="outlined"
                                onPress={() => {
                                    resetProfileForm();
                                    setEditMode(null);
                                }}
                                disabled={updating}
                            >
                                {t('common.cancel', { defaultValue: 'Cancel' })}
                            </Button>
                            <Button
                                flex={1}
                                backgroundColor="#67B329"
                                onPress={handleUpdateProfile}
                                disabled={updating}
                            >
                                {updating ? <ActivityIndicator size="small" color="#fff" /> : t('common.save', { defaultValue: 'Save Changes' })}
                            </Button>
                        </XStack>
                    </YStack>
                </Sheet.Frame>
            </Sheet>

            {/* Password Change Modal */}
            <Sheet
                modal
                open={editMode === 'password'}
                onOpenChange={(open: boolean) => !open && setEditMode(null)}
                snapPoints={[60]}
                dismissOnSnapToBottom
            >
                <Sheet.Overlay />
                <Sheet.Handle />
                <Sheet.Frame padding="$4" gap="$4">
                    <H4>{t('auth.changePassword', { defaultValue: 'Change Password' })}</H4>
                    <Separator />

                    <YStack gap="$3">
                        <YStack gap="$2">
                            <Label htmlFor="currentPassword">{t('profile.currentPassword', { defaultValue: 'Current Password' })}</Label>
                            <Input
                                id="currentPassword"
                                value={passwordForm.currentPassword}
                                onChangeText={(text) => setPasswordForm(prev => ({ ...prev, currentPassword: text }))}
                                placeholder={t('profile.enterCurrentPassword', { defaultValue: 'Enter current password' })}
                                secureTextEntry
                            />
                        </YStack>

                        <YStack gap="$2">
                            <Label htmlFor="newPassword">{t('auth.newPassword', { defaultValue: 'New Password' })}</Label>
                            <Input
                                id="newPassword"
                                value={passwordForm.newPassword}
                                onChangeText={(text) => setPasswordForm(prev => ({ ...prev, newPassword: text }))}
                                placeholder={t('auth.enterNewPassword', { defaultValue: 'Enter new password (min 6 characters)' })}
                                secureTextEntry
                            />
                        </YStack>
                    </YStack>

                    <XStack gap="$3" marginTop="$4">
                        <Button
                            flex={1}
                            variant="outlined"
                            onPress={() => {
                                setPasswordForm({ currentPassword: '', newPassword: '' });
                                setEditMode(null);
                            }}
                            disabled={updating}
                        >
                            {t('common.cancel', { defaultValue: 'Cancel' })}
                        </Button>
                        <Button
                            flex={1}
                            backgroundColor="#67B329"
                            onPress={handleChangePassword}
                            disabled={updating}
                        >
                            {updating ? <ActivityIndicator size="small" color="#fff" /> : t('auth.changePassword', { defaultValue: 'Change Password' })}
                        </Button>
                    </XStack>
                </Sheet.Frame>
            </Sheet>

        </YStack>
    );
};