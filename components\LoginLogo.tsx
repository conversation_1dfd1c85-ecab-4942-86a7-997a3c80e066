import { Animated, View } from 'react-native';
import { useEffect, useRef } from 'react';
import { LinearGradient } from 'expo-linear-gradient';

export default function LoginLogo() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const lightningAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Staggered animation sequence
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      Animated.loop(
        Animated.sequence([
          Animated.timing(lightningAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(lightningAnim, {
            toValue: 0,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ),
    ]).start();
  }, []);

  return (
    <Animated.View style={{
      alignSelf: 'center',
      opacity: fadeAnim,
      transform: [{ scale: scaleAnim }]
    }}>
      {/* Professional Logo Container */}
      <View style={{
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 30,
        paddingHorizontal: 20,
      }}>

        {/* Logo Background Glow */}
        <View style={{
          position: 'absolute',
          width: 280,
          height: 120,
          borderRadius: 60,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          shadowColor: '#FFFFFF',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 10,
        }} />

        {/* Lightning Icon Container */}
        <Animated.View style={{
          marginBottom: 15,
          opacity: lightningAnim,
          transform: [{
            scale: lightningAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [1, 1.2],
            })
          }]
        }}>
          <View style={{
            width: 60,
            height: 60,
            borderRadius: 30,
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            alignItems: 'center',
            justifyContent: 'center',
            borderWidth: 2,
            borderColor: 'rgba(255, 255, 255, 0.3)',
            shadowColor: '#FFFFFF',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.4,
            shadowRadius: 8,
            elevation: 8,
          }}>
            <Animated.Text style={{
              fontSize: 28,
              color: '#FFD700',
              textShadowColor: 'rgba(255, 215, 0, 0.8)',
              textShadowOffset: { width: 0, height: 0 },
              textShadowRadius: 10,
            }}>
              ⚡
            </Animated.Text>
          </View>
        </Animated.View>

        {/* Main Logo Text */}
        <Animated.Text style={{
          fontSize: 52,
          fontWeight: '900',
          color: '#FFFFFF',
          letterSpacing: 3,
          textAlign: 'center',
          marginBottom: 8,
          textShadowColor: 'rgba(0, 0, 0, 0.5)',
          textShadowOffset: { width: 0, height: 3 },
          textShadowRadius: 6,
          fontFamily: 'System', // Use system font for consistency
        }}>
          BolTalab
        </Animated.Text>

        {/* Decorative Line */}
        <View style={{
          width: 120,
          height: 2,
          backgroundColor: 'rgba(255, 255, 255, 0.6)',
          marginBottom: 12,
          borderRadius: 1,
          shadowColor: '#FFFFFF',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.5,
          shadowRadius: 4,
        }} />

        {/* Tagline */}
        <Animated.Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: '#FFFFFF',
          letterSpacing: 1.5,
          textAlign: 'center',
          fontStyle: 'italic',
          opacity: 0.95,
          textShadowColor: 'rgba(0, 0, 0, 0.3)',
          textShadowOffset: { width: 0, height: 1 },
          textShadowRadius: 3,
        }}>
          Fast As Lightning
        </Animated.Text>

        {/* Subtitle */}
        <Animated.Text style={{
          fontSize: 14,
          fontWeight: '500',
          color: '#FFFFFF',
          letterSpacing: 0.5,
          textAlign: 'center',
          opacity: 0.8,
          marginTop: 8,
          textShadowColor: 'rgba(0, 0, 0, 0.2)',
          textShadowOffset: { width: 0, height: 1 },
          textShadowRadius: 2,
        }}>
          Premium Delivery Platform
        </Animated.Text>

        {/* Floating Particles Effect */}
        <Animated.View style={{
          position: 'absolute',
          top: 20,
          right: 30,
          opacity: lightningAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.3, 0.8],
          }),
        }}>
          <View style={{
            width: 4,
            height: 4,
            borderRadius: 2,
            backgroundColor: '#FFD700',
            shadowColor: '#FFD700',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 1,
            shadowRadius: 4,
          }} />
        </Animated.View>

        <Animated.View style={{
          position: 'absolute',
          top: 40,
          left: 25,
          opacity: lightningAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.5, 0.9],
          }),
        }}>
          <View style={{
            width: 3,
            height: 3,
            borderRadius: 1.5,
            backgroundColor: '#FFFFFF',
            shadowColor: '#FFFFFF',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.8,
            shadowRadius: 3,
          }} />
        </Animated.View>

        <Animated.View style={{
          position: 'absolute',
          bottom: 30,
          right: 20,
          opacity: lightningAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.4, 0.7],
          }),
        }}>
          <View style={{
            width: 2,
            height: 2,
            borderRadius: 1,
            backgroundColor: '#67B329',
            shadowColor: '#67B329',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 1,
            shadowRadius: 2,
          }} />
        </Animated.View>
      </View>
    </Animated.View>
  )
}