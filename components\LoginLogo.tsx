import { Animated } from 'react-native';
import { useEffect, useRef } from 'react';

export default function LoginLogo() {
  const fadeAnim = useRef(new Animated.Value(0)).current

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1500,
      useNativeDriver: true,
    }).start()
  }, [])

  return (
    <Animated.View style={{ alignSelf: 'center', opacity: fadeAnim }}>
      {/* Professional Logo Design */}
      <Animated.View style={{
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 20,
      }}>
        {/* Main Logo Text */}
        <Animated.Text style={{
          fontSize: 48,
          fontWeight: '800',
          color: '#7529B3',
          letterSpacing: 2,
          textAlign: 'center',
          marginBottom: 8,
          textShadowColor: 'rgba(117, 41, 179, 0.3)',
          textShadowOffset: { width: 0, height: 2 },
          textShadowRadius: 4,
        }}>
          BolTalab
        </Animated.Text>

        {/* Tagline */}
        <Animated.Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#67B329',
          letterSpacing: 1,
          textAlign: 'center',
          fontStyle: 'italic',
        }}>
          Fast As Lightning ⚡
        </Animated.Text>
      </Animated.View>
    </Animated.View>
  )
}