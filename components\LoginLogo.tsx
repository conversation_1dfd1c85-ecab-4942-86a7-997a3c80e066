import { Animated, View, Dimensions } from 'react-native';
import { useEffect, useRef } from 'react';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

export default function LoginLogo() {
  // Responsive scaling
  const isMobile = width < 768;
  const scale = isMobile ? 0.75 : 1;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.7)).current;
  const lightningAnim = useRef(new Animated.Value(0)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const shimmerAnim = useRef(new Animated.Value(0)).current;
  const particleAnim1 = useRef(new Animated.Value(0)).current;
  const particleAnim2 = useRef(new Animated.Value(0)).current;
  const particleAnim3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Complex staggered animation sequence
    Animated.sequence([
      // Initial entrance
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 40,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]),
      // Continuous animations
      Animated.parallel([
        // Lightning pulse
        Animated.loop(
          Animated.sequence([
            Animated.timing(lightningAnim, {
              toValue: 1,
              duration: 1200,
              useNativeDriver: true,
            }),
            Animated.timing(lightningAnim, {
              toValue: 0.3,
              duration: 1200,
              useNativeDriver: true,
            }),
          ])
        ),
        // Rotation animation
        Animated.loop(
          Animated.timing(rotateAnim, {
            toValue: 1,
            duration: 20000,
            useNativeDriver: true,
          })
        ),
        // Pulse animation
        Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnim, {
              toValue: 1.05,
              duration: 2000,
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnim, {
              toValue: 1,
              duration: 2000,
              useNativeDriver: true,
            }),
          ])
        ),
        // Shimmer effect
        Animated.loop(
          Animated.timing(shimmerAnim, {
            toValue: 1,
            duration: 3000,
            useNativeDriver: true,
          })
        ),
        // Particle animations
        Animated.loop(
          Animated.timing(particleAnim1, {
            toValue: 1,
            duration: 4000,
            useNativeDriver: true,
          })
        ),
        Animated.loop(
          Animated.timing(particleAnim2, {
            toValue: 1,
            duration: 5000,
            useNativeDriver: true,
          })
        ),
        Animated.loop(
          Animated.timing(particleAnim3, {
            toValue: 1,
            duration: 6000,
            useNativeDriver: true,
          })
        ),
      ]),
    ]).start();
  }, []);

  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const shimmerTranslate = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-width, width],
  });

  return (
    <Animated.View style={{
      alignSelf: 'center',
      opacity: fadeAnim,
      transform: [
        { scale: scaleAnim },
        { scale: pulseAnim },
        { scale: scale } // Responsive scaling
      ]
    }}>
      {/* Responsive Professional Logo Container */}
      <View style={{
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: isMobile ? 15 : 25,
        paddingHorizontal: isMobile ? 15 : 20,
        position: 'relative',
      }}>

        {/* Outer Glow Ring - Reduced */}
        <Animated.View style={{
          position: 'absolute',
          width: 280,
          height: 140,
          borderRadius: 70,
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          opacity: glowAnim,
          transform: [{ rotate: rotateInterpolate }],
          shadowColor: '#FFFFFF',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.4,
          shadowRadius: 25,
          elevation: 15,
        }} />

        {/* Inner Glow - Reduced */}
        <Animated.View style={{
          position: 'absolute',
          width: 260,
          height: 125,
          borderRadius: 62.5,
          backgroundColor: 'rgba(255, 255, 255, 0.08)',
          opacity: glowAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0, 0.8],
          }),
          shadowColor: '#FFFFFF',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.3,
          shadowRadius: 20,
          elevation: 12,
        }} />

        {/* Premium Background Gradient - Reduced */}
        <View style={{
          position: 'absolute',
          width: 240,
          height: 110,
          borderRadius: 55,
          backgroundColor: 'rgba(255, 255, 255, 0.12)',
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.2)',
          shadowColor: '#FFFFFF',
          shadowOffset: { width: 0, height: 6 },
          shadowOpacity: 0.25,
          shadowRadius: 15,
          elevation: 10,
        }} />

        {/* Shimmer Effect Overlay - Reduced */}
        <Animated.View style={{
          position: 'absolute',
          width: 240,
          height: 110,
          borderRadius: 55,
          overflow: 'hidden',
        }}>
          <Animated.View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: 80,
            height: '100%',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            transform: [{ translateX: shimmerTranslate }],
            opacity: 0.6,
          }} />
        </Animated.View>

        {/* Compact Lightning Icon Container */}
        <Animated.View style={{
          marginBottom: 12,
          opacity: lightningAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.7, 1],
          }),
          transform: [{
            scale: lightningAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [1, 1.15],
            })
          }, {
            rotate: lightningAnim.interpolate({
              inputRange: [0, 1],
              outputRange: ['0deg', '5deg'],
            })
          }]
        }}>
          {/* Lightning Outer Ring - Reduced */}
          <View style={{
            width: 60,
            height: 60,
            borderRadius: 30,
            backgroundColor: 'rgba(255, 215, 0, 0.15)',
            alignItems: 'center',
            justifyContent: 'center',
            borderWidth: 2,
            borderColor: 'rgba(255, 215, 0, 0.4)',
            shadowColor: '#FFD700',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.6,
            shadowRadius: 12,
            elevation: 12,
          }}>
            {/* Lightning Inner Ring - Reduced */}
            <View style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              backgroundColor: 'rgba(255, 255, 255, 0.25)',
              alignItems: 'center',
              justifyContent: 'center',
              borderWidth: 1,
              borderColor: 'rgba(255, 255, 255, 0.4)',
              shadowColor: '#FFFFFF',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.3,
              shadowRadius: 6,
            }}>
              <Animated.Text style={{
                fontSize: 24,
                color: '#FFD700',
                textShadowColor: 'rgba(255, 215, 0, 1)',
                textShadowOffset: { width: 0, height: 0 },
                textShadowRadius: 10,
                transform: [{
                  scale: lightningAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.1],
                  })
                }]
              }}>
                ⚡
              </Animated.Text>
            </View>
          </View>
        </Animated.View>

        {/* Compact Main Logo Text */}
        <Animated.Text style={{
          fontSize: 44,
          fontWeight: '900',
          color: '#FFFFFF',
          letterSpacing: 3,
          textAlign: 'center',
          marginBottom: 10,
          textShadowColor: 'rgba(0, 0, 0, 0.6)',
          textShadowOffset: { width: 0, height: 3 },
          textShadowRadius: 6,
          fontFamily: 'System',
          transform: [{
            scale: pulseAnim.interpolate({
              inputRange: [1, 1.05],
              outputRange: [1, 1.02],
            })
          }]
        }}>
          BolTalab
        </Animated.Text>

        {/* Compact Decorative Elements */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 12,
          gap: 8,
        }}>
          {/* Left Ornament - Reduced */}
          <View style={{
            width: 30,
            height: 2,
            backgroundColor: 'rgba(255, 215, 0, 0.8)',
            borderRadius: 1,
            shadowColor: '#FFD700',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.8,
            shadowRadius: 4,
          }} />

          {/* Center Diamond - Reduced */}
          <Animated.View style={{
            width: 8,
            height: 8,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            transform: [{ rotate: '45deg' }],
            shadowColor: '#FFFFFF',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.8,
            shadowRadius: 6,
            opacity: lightningAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.6, 1],
            }),
          }} />

          {/* Right Ornament - Reduced */}
          <View style={{
            width: 30,
            height: 2,
            backgroundColor: 'rgba(255, 215, 0, 0.8)',
            borderRadius: 1,
            shadowColor: '#FFD700',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.8,
            shadowRadius: 4,
          }} />
        </View>

        {/* Compact Tagline */}
        <Animated.Text style={{
          fontSize: 16,
          fontWeight: '700',
          color: '#FFFFFF',
          letterSpacing: 1.5,
          textAlign: 'center',
          fontStyle: 'italic',
          opacity: 0.98,
          textShadowColor: 'rgba(0, 0, 0, 0.4)',
          textShadowOffset: { width: 0, height: 2 },
          textShadowRadius: 3,
          marginBottom: 6,
        }}>
          Fast As Lightning
        </Animated.Text>

        {/* Compact Subtitle */}
        <Animated.Text style={{
          fontSize: 13,
          fontWeight: '600',
          color: '#FFFFFF',
          letterSpacing: 0.8,
          textAlign: 'center',
          opacity: 0.85,
          marginBottom: 8,
          textShadowColor: 'rgba(0, 0, 0, 0.3)',
          textShadowOffset: { width: 0, height: 1 },
          textShadowRadius: 2,
        }}>
          Premium Delivery Platform
        </Animated.Text>

        {/* Compact Professional Badge */}
        <View style={{
          paddingHorizontal: 16,
          paddingVertical: 6,
          backgroundColor: 'rgba(255, 255, 255, 0.15)',
          borderRadius: 15,
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.3)',
          shadowColor: '#FFFFFF',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
        }}>
          <Animated.Text style={{
            fontSize: 10,
            fontWeight: '600',
            color: '#FFFFFF',
            letterSpacing: 0.8,
            textAlign: 'center',
            opacity: 0.9,
            textShadowColor: 'rgba(0, 0, 0, 0.2)',
            textShadowOffset: { width: 0, height: 1 },
            textShadowRadius: 1,
          }}>
            ENTERPRISE SOLUTION
          </Animated.Text>
        </View>

        {/* Compact Floating Particles System */}
        {/* Gold Particle 1 */}
        <Animated.View style={{
          position: 'absolute',
          top: 20,
          right: 25,
          opacity: particleAnim1.interpolate({
            inputRange: [0, 0.5, 1],
            outputRange: [0.3, 1, 0.3],
          }),
          transform: [{
            translateY: particleAnim1.interpolate({
              inputRange: [0, 1],
              outputRange: [0, -20],
            })
          }, {
            scale: particleAnim1.interpolate({
              inputRange: [0, 0.5, 1],
              outputRange: [1, 1.5, 1],
            })
          }]
        }}>
          <View style={{
            width: 6,
            height: 6,
            borderRadius: 3,
            backgroundColor: '#FFD700',
            shadowColor: '#FFD700',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 1,
            shadowRadius: 8,
          }} />
        </Animated.View>

        {/* White Particle 2 */}
        <Animated.View style={{
          position: 'absolute',
          top: 40,
          left: 25,
          opacity: particleAnim2.interpolate({
            inputRange: [0, 0.3, 0.7, 1],
            outputRange: [0.2, 0.8, 0.8, 0.2],
          }),
          transform: [{
            translateX: particleAnim2.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 15],
            })
          }, {
            rotate: particleAnim2.interpolate({
              inputRange: [0, 1],
              outputRange: ['0deg', '180deg'],
            })
          }]
        }}>
          <View style={{
            width: 4,
            height: 4,
            borderRadius: 2,
            backgroundColor: '#FFFFFF',
            shadowColor: '#FFFFFF',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 1,
            shadowRadius: 6,
          }} />
        </Animated.View>

        {/* Green Particle 3 */}
        <Animated.View style={{
          position: 'absolute',
          bottom: 25,
          right: 20,
          opacity: particleAnim3.interpolate({
            inputRange: [0, 0.4, 0.8, 1],
            outputRange: [0.1, 0.7, 0.7, 0.1],
          }),
          transform: [{
            translateY: particleAnim3.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 25],
            })
          }]
        }}>
          <View style={{
            width: 5,
            height: 5,
            borderRadius: 2.5,
            backgroundColor: '#67B329',
            shadowColor: '#67B329',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 1,
            shadowRadius: 7,
          }} />
        </Animated.View>

        {/* Purple Particle 4 */}
        <Animated.View style={{
          position: 'absolute',
          bottom: 40,
          left: 25,
          opacity: lightningAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.2, 0.9],
          }),
          transform: [{
            scale: lightningAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.8, 1.3],
            })
          }]
        }}>
          <View style={{
            width: 3,
            height: 3,
            borderRadius: 1.5,
            backgroundColor: '#7529B3',
            shadowColor: '#7529B3',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 1,
            shadowRadius: 5,
          }} />
        </Animated.View>

        {/* Floating Stars */}
        <Animated.View style={{
          position: 'absolute',
          top: 30,
          right: 40,
          opacity: particleAnim1.interpolate({
            inputRange: [0, 1],
            outputRange: [0.4, 0.8],
          }),
          transform: [{
            rotate: particleAnim1.interpolate({
              inputRange: [0, 1],
              outputRange: ['0deg', '360deg'],
            })
          }]
        }}>
          <Animated.Text style={{
            fontSize: 12,
            color: '#FFD700',
            textShadowColor: 'rgba(255, 215, 0, 0.8)',
            textShadowOffset: { width: 0, height: 0 },
            textShadowRadius: 6,
          }}>
            ✨
          </Animated.Text>
        </Animated.View>

        <Animated.View style={{
          position: 'absolute',
          bottom: 35,
          left: 40,
          opacity: particleAnim2.interpolate({
            inputRange: [0, 1],
            outputRange: [0.3, 0.7],
          }),
          transform: [{
            rotate: particleAnim2.interpolate({
              inputRange: [0, 1],
              outputRange: ['360deg', '0deg'],
            })
          }]
        }}>
          <Animated.Text style={{
            fontSize: 10,
            color: '#FFFFFF',
            textShadowColor: 'rgba(255, 255, 255, 0.8)',
            textShadowOffset: { width: 0, height: 0 },
            textShadowRadius: 4,
          }}>
            ✨
          </Animated.Text>
        </Animated.View>
      </View>
    </Animated.View>
  )
}