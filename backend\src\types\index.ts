import { Request } from 'express';
import { IUser } from '../models/User';

export interface AuthenticatedRequest extends Request {
  user?: IUser;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  // Basic Information
  firstName: string;
  lastName: string;
  email: string;

  // Contact & Security
  phoneNumber: string;
  password: string;

  // Profile Setup
  username: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';

  // Address Information
  address: string;
  city: string;
  country: string;

  // Account Type
  role: 'customer' | 'supplier';

  // Business Information (for suppliers)
  supplierId?: string;
  storeName?: string;
  businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  openHours?: string;

  // Location & Preferences
  location?: [number, number]; // [longitude, latitude]
  notifications?: boolean;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface VerifyResetCodeRequest {
  email: string;
  code: string;
}

export interface ResetPasswordRequest {
  email: string;
  code: string;
  password: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface UserQuery extends PaginationQuery {
  role?: string;
  isActive?: boolean;
  search?: string;
}
