import React, { useState } from 'react';
import { Truck } from 'lucide-react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showText?: boolean;
}

const Logo: React.FC<LogoProps> = ({
  size = 'md',
  className = '',
  showText = true
}) => {
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    sm: 'h-8',
    md: 'h-12',
    lg: 'h-16',
    xl: 'h-20'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-3xl',
    xl: 'text-4xl'
  };

  const iconSizeClasses = {
    sm: 20,
    md: 32,
    lg: 40,
    xl: 48
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {!imageError ? (
        <img
          src="/wasel-logo.png"
          alt="Wasel Logo"
          className={`${sizeClasses[size]} w-auto object-contain`}
          onError={() => setImageError(true)}
        />
      ) : (
        // Fallback icon if logo fails to load
        <>
        <div className={`${sizeClasses[size]} flex items-center justify-center bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl`}>
          <Truck size={iconSizeClasses[size]} className="text-white" />
        </div>
        <span className={`font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent ${textSizeClasses[size]}`}>
          Wasel
        </span>
        </>
      )}
    </div>
  );
};

export default Logo;
