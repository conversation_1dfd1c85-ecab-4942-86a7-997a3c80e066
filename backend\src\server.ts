import express, { Request, Response } from 'express';
import dotenv from 'dotenv';
import morgan from 'morgan';
import compression from 'compression';
import cors from 'cors';

// Load environment variables
dotenv.config();

// Internal imports
import connectDB from './config/database';
import { errorHandler, notFound } from './middleware/errorHandler';
import { generalLimiter, corsOptions, helmetConfig } from './middleware/security';

// Routes
import authRoutes from './routes/auth';
import userRoutes from './routes/user';
import aiChatRoutes from './routes/aiChatRoutes';
import servicesRoutes from './routes/services';
import categoriesRoutes from './routes/categories';
import suppliersRoutes from './routes/suppliers';
import ordersRoutes from './routes/orders';
import packagesRoutes from './routes/packages';

const app = express();
const PORT = process.env.PORT || 3000;

// Connect to MongoDB
connectDB();

// Security middleware
app.use(helmetConfig);
app.use(cors(corsOptions));
app.use(generalLimiter);

// Body parsers
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression
app.use(compression());

// Logging
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  });
});

// API Documentation
app.get('/api', (req: Request, res: Response) => {
  res.status(200).json({
    success: true,
    message: 'Wasel API v1.0.0',
    endpoints: {
      auth: {
        'POST /api/auth/signup': 'Create a new user account',
        'POST /api/auth/login': 'Login with email and password',
        'POST /api/auth/refresh-token': 'Refresh access token',
        'POST /api/auth/forgot-password': 'Request password reset',
        'POST /api/auth/reset-password': 'Reset password with token',
        'POST /api/auth/logout': 'Logout (requires auth)',
        'POST /api/auth/logout-all': 'Logout from all devices (requires auth)',
      },
      users: {
        'GET /api/users/profile': 'Get current user profile (requires auth)',
        'PUT /api/users/profile': 'Update user profile (requires auth)',
        'PUT /api/users/change-password': 'Change password (requires auth)',
        'DELETE /api/users/account': 'Deactivate account (requires auth)',
        'GET /api/users/:userId': 'Get user by ID (requires auth)',
      },
      aiChat: {
        'POST /api/ai-chat/send-message': 'Send message to AI assistant',
        'POST /api/ai-chat/message': 'Send message to AI assistant (requires auth)',
        'GET /api/ai-chat/conversations': 'Get user conversations (requires auth)',
        'GET /api/ai-chat/conversation/:id': 'Get specific conversation (requires auth)',
        'DELETE /api/ai-chat/conversation/:id': 'Delete conversation (requires auth)',
        'POST /api/ai-chat/feedback': 'Submit AI response feedback (requires auth)',
        'GET /api/ai-chat/suggestions': 'Get personalized suggestions (requires auth)',
        'POST /api/ai-chat/context': 'Update user context (requires auth)',
      },
      services: {
        'GET /api/services': 'Get all active services',
        'GET /api/services/:key': 'Get service by key',
      },
      categories: {
        'GET /api/categories': 'Get all active categories',
        'GET /api/categories/:key': 'Get category by key',
      },
      suppliers: {
        'GET /api/suppliers': 'Get suppliers with filtering and pagination',
        'GET /api/suppliers/category/:category': 'Get suppliers by category',
        'GET /api/suppliers/:id': 'Get supplier by ID',
        'GET /api/suppliers/:id/products': 'Get supplier products',
        'GET /api/suppliers/:supplierId/products/:productId': 'Get specific product',
      },
      orders: {
        'POST /api/orders': 'Create new order (requires auth)',
        'GET /api/orders': 'Get user orders (requires auth)',
        'GET /api/orders/:orderId': 'Get order by ID (requires auth)',
        'PUT /api/orders/:orderId/status': 'Update order status (requires auth)',
      },
      packages: {
        'POST /api/packages': 'Create package delivery request (requires auth)',
        'POST /api/packages/request-pickup': 'Request package pickup (requires auth)',
        'GET /api/packages': 'Get user packages (requires auth)',
        'GET /api/packages/:trackingNumber': 'Get package by tracking number (requires auth)',
        'PUT /api/packages/:trackingNumber/status': 'Update package status (requires auth)',
      },
    },
  });
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/ai-chat', aiChatRoutes);
app.use('/api/services', servicesRoutes);
app.use('/api/categories', categoriesRoutes);
app.use('/api/suppliers', suppliersRoutes);
app.use('/api/orders', ordersRoutes);
app.use('/api/packages', packagesRoutes);

// 404 and error handlers
app.use(notFound);
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`
🚀 Server is running on port ${PORT}
📝 Environment: ${process.env.NODE_ENV}
🌐 API Documentation: http://localhost:${PORT}/api
❤️  Health Check: http://localhost:${PORT}/health
  `);
});

export default app;
