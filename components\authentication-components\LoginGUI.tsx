import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ck, H4, H6, <PERSON>, Theme, Card } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { Link, useRouter } from 'expo-router';
import LoginLogo from '../LoginLogo';
import { Alert, ScrollView, Dimensions } from 'react-native';
import { useCurrentUserData } from '../useCurrentUserData';
import { apiService } from '../../services/apiService';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useTranslation } from 'react-i18next';
import { useLanguageStore } from '../../stores/languageStore';
import { ModernLanguageSwitcher } from '../common/ModernLanguageSwitcher';
import { showErrorAlert } from '../../utils/errorDisplay';

type LoginGUIProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
}

export const LoginGUI = ({ children, methods }: LoginGUIProps) => {
    const { t, ready } = useTranslation();
    const { isRTL } = useLanguageStore();
    const router = useRouter();
    const { handleSubmit, getValues } = methods;
    const { setCurrentUser, setLoading, setError, loadUserProfile } = useCurrentUserData();
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Don't render until translations are ready
    if (!ready) {
        return null;
    }

    const onSubmit = async () => {
      const { ['username or email']: email, password } = getValues();

      if (!email || !password) {
        showErrorAlert(
          { message: 'Please enter both email and password.' },
          t
        );
        return;
      }

      setIsSubmitting(true);
      setLoading(true);
      setError(null);

      try {
        const response = await apiService.login(
          email.trim().toLowerCase(),
          password
        );

        if (response.success && response.data) {
          // Set user in store
          setCurrentUser(response.data.user);

          // Also load user profile to ensure state is properly initialized
          try {
            await loadUserProfile();
            console.log('✅ User profile loaded after login');
          } catch (profileError) {
            console.log('⚠️ Profile load failed after login, but continuing:', profileError);
          }

          // Redirect based on role
          if (response.data.user.role === 'customer') {
            router.push('/(customer-pages)/home');
          } else if (response.data.user.role === 'supplier') {
            router.push('/(supplier-pages)/home');
          }
        } else {
          showErrorAlert(response, t);
          setError(response.message || t('auth.loginFailed', { defaultValue: 'Login failed' }));
        }
      } catch (error) {
        showErrorAlert(error, t);
        const errorMessage = error instanceof Error ? error.message : t('auth.networkError', { defaultValue: 'Network error occurred' });
        setError(errorMessage);
      } finally {
        setIsSubmitting(false);
        setLoading(false);
      }
    };


    const { height, width } = Dimensions.get('window');

    // Responsive breakpoints
    const isMobile = width < 768;
    const isTablet = width >= 768 && width < 1024;
    const isDesktop = width >= 1024;

    // Responsive values
    const headerPadding = isMobile ? "$4" : "$6";
    const headerPaddingTop = isMobile ? "$6" : "$8";
    const headerGap = isMobile ? "$2" : "$3";
    const borderRadius = isMobile ? "$8" : "$12";
    const logoScale = isMobile ? 0.8 : 1;

    return (
        <Theme name="light">
            <FormProvider {...methods}>
                <YStack height={height} backgroundColor="$background">
                    {/* Responsive Header Section */}
                    <YStack
                        padding={headerPadding}
                        paddingTop={headerPaddingTop}
                        backgroundColor="$primary"
                        borderBottomLeftRadius={borderRadius}
                        borderBottomRightRadius={borderRadius}
                        alignItems="center"
                        gap={headerGap}
                        position="relative"
                        minHeight={isMobile ? 280 : 320}
                    >
                        {/* Language Switcher */}
                        <XStack
                            position="absolute"
                            top={isMobile ? "$4" : "$6"}
                            right={isMobile ? "$4" : "$6"}
                            zIndex={1000}
                        >
                            <ModernLanguageSwitcher variant="compact" />
                        </XStack>

                        {/* Responsive Logo */}
                        <YStack transform={[{ scale: logoScale }]}>
                            <LoginLogo />
                        </YStack>

                        <H4
                            color="white"
                            fontWeight="bold"
                            textAlign="center"
                            fontSize={isMobile ? "$5" : "$6"}
                            paddingHorizontal={isMobile ? "$4" : "$0"}
                        >
                            {t('auth.welcomeBack', { defaultValue: 'Welcome Back!' })}
                        </H4>
                        <Text
                            color="white"
                            fontSize={isMobile ? "$3" : "$4"}
                            textAlign="center"
                            opacity={0.9}
                            paddingHorizontal={isMobile ? "$4" : "$0"}
                        >
                            {t('auth.signInToContinue', { defaultValue: 'Sign in to continue your journey' })}
                        </Text>
                    </YStack>

                    {/* Responsive Content Section */}
                    <YStack flex={1}>
                        <ScrollView
                            contentContainerStyle={{
                                flexGrow: 1,
                                padding: isMobile ? 16 : 24,
                                paddingBottom: isMobile ? 100 : 120
                            }}
                            showsVerticalScrollIndicator={false}
                        >
                            <MotiView
                                from={{ opacity: 0, translateY: 50 }}
                                animate={{ opacity: 1, translateY: 0 }}
                                transition={{ type: 'timing', duration: 500 }}
                            >
                                <Card
                                    padding={isMobile ? "$4" : "$6"}
                                    borderRadius={isMobile ? "$4" : "$6"}
                                    backgroundColor="white"
                                    shadowColor="$shadowColor"
                                    shadowOffset={{ width: 0, height: 4 }}
                                    shadowOpacity={0.1}
                                    shadowRadius={8}
                                    elevation={5}
                                    marginTop={isMobile ? "$2" : "$4"}
                                    marginHorizontal={isMobile ? "$2" : "$0"}
                                >
                                    <YStack gap={isMobile ? "$4" : "$5"}>
                                        <YStack gap="$2" alignItems="center">
                                            <Ionicons
                                                name="log-in"
                                                size={isMobile ? 28 : 32}
                                                color="#7529B3"
                                            />
                                            <Text
                                                fontSize={isMobile ? "$4" : "$5"}
                                                fontWeight="600"
                                                color="$gray11"
                                                textAlign="center"
                                                paddingHorizontal={isMobile ? "$2" : "$0"}
                                            >
                                                {t('auth.signInToAccount', { defaultValue: 'Sign In to Your Account' })}
                                            </Text>
                                        </YStack>

                                        <YStack gap={isMobile ? "$3" : "$4"}>
                                            <CustomTextField
                                                name="username or email"
                                                icon="person"
                                                label={t('auth.usernameOrEmail', { defaultValue: 'Username or Email' })}
                                                placeholder={t('auth.enterUsernameOrEmail', { defaultValue: 'Enter your username or email' })}
                                                keyboardType="email-address"
                                                autoCapitalize="none"
                                                required
                                            />

                                            <CustomTextField
                                                name="password"
                                                icon="lock-closed"
                                                label={t('auth.password', { defaultValue: 'Password' })}
                                                secureTextEntry
                                                placeholder={t('auth.enterPassword', { defaultValue: 'Enter your password' })}
                                                required
                                            />
                                        </YStack>

                                        <Link
                                            href="/authentication/verify-before-reset1"
                                            asChild
                                        >
                                            <Text
                                                color="$primary"
                                                fontSize={isMobile ? "$3" : "$4"}
                                                fontWeight="500"
                                                textAlign="center"
                                                textDecorationLine="underline"
                                                padding={isMobile ? "$1" : "$2"}
                                            >
                                                {t('auth.forgotPassword', { defaultValue: 'Forgot Password? Click Here' })}
                                            </Text>
                                        </Link>
                                    </YStack>
                                </Card>
                            </MotiView>
                        </ScrollView>
                    </YStack>

                    {/* Responsive Bottom Buttons */}
                    <YStack
                        backgroundColor="white"
                        padding={isMobile ? "$3" : "$4"}
                        borderTopWidth={1}
                        borderTopColor="$borderColor"
                        shadowColor="$shadowColor"
                        shadowOffset={{ width: 0, height: -2 }}
                        shadowOpacity={0.1}
                        shadowRadius={4}
                        elevation={5}
                    >
                        {isMobile ? (
                            // Mobile: Stack buttons vertically
                            <YStack gap="$3">
                                <Button
                                    backgroundColor="$primary"
                                    color="white"
                                    onPress={handleSubmit(onSubmit)}
                                    disabled={isSubmitting}
                                    icon={
                                        isSubmitting ? (
                                            <Ionicons name="hourglass" size={18} color="white" />
                                        ) : (
                                            <Ionicons name="log-in" size={18} color="white" />
                                        )
                                    }
                                    fontSize="$4"
                                    fontWeight="600"
                                    height={50}
                                >
                                    {isSubmitting ? t('auth.signingIn', { defaultValue: 'Signing In...' }) : t('auth.login', { defaultValue: 'Sign In' })}
                                </Button>

                                <Link href={{ pathname: '/authentication/signup', params: { name: 'Dan' } }} asChild>
                                    <Button
                                        backgroundColor="$secondary"
                                        hoverStyle={{ bg: '$secondary_strong' }}
                                        pressStyle={{ bg: '$secondary_strong' }}
                                        color="white"
                                        icon={<Ionicons name="person-add" size={18} color="white" />}
                                        fontSize="$4"
                                        fontWeight="600"
                                        height={50}
                                    >
                                        {t('auth.signup', { defaultValue: 'Sign Up' })}
                                    </Button>
                                </Link>
                            </YStack>
                        ) : (
                            // Tablet/Desktop: Keep horizontal layout
                            <XStack gap="$3" justifyContent="space-between">
                                <Button
                                    flex={1}
                                    backgroundColor="$primary"
                                    color="white"
                                    onPress={handleSubmit(onSubmit)}
                                    disabled={isSubmitting}
                                    icon={
                                        isSubmitting ? (
                                            <Ionicons name="hourglass" size={20} color="white" />
                                        ) : (
                                            <Ionicons name="log-in" size={20} color="white" />
                                        )
                                    }
                                    fontSize="$5"
                                    fontWeight="600"
                                    height={56}
                                >
                                    {isSubmitting ? t('auth.signingIn', { defaultValue: 'Signing In...' }) : t('auth.login', { defaultValue: 'Sign In' })}
                                </Button>

                                <Link href={{ pathname: '/authentication/signup', params: { name: 'Dan' } }} asChild>
                                    <Button
                                        flex={1}
                                        backgroundColor="$secondary"
                                        hoverStyle={{ bg: '$secondary_strong' }}
                                        pressStyle={{ bg: '$secondary_strong' }}
                                        color="white"
                                        icon={<Ionicons name="person-add" size={20} color="white" />}
                                        fontSize="$5"
                                        fontWeight="600"
                                        height={56}
                                    >
                                        {t('auth.signup', { defaultValue: 'Sign Up' })}
                                    </Button>
                                </Link>
                            </XStack>
                        )}

                        <Text
                            fontSize={isMobile ? "$2" : "$3"}
                            color="$gray9"
                            textAlign="center"
                            marginTop="$3"
                            paddingHorizontal={isMobile ? "$2" : "$0"}
                        >
                            {t('auth.noAccount', { defaultValue: "Don't have an account? Create one now!" })}
                        </Text>
                    </YStack>

                    {children}
                </YStack>
            </FormProvider>
        </Theme>
    );
};