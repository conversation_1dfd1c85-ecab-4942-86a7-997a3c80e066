import React, { useState, useEffect } from 'react';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { Alert } from 'react-native';
import { YStack, XStack, H2, Text, Button, Input, Label, Card, Separator } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { Container } from '~/components/Container';
import { apiService } from '~/services/apiService';
import { useTranslation } from 'react-i18next';

export default function ResetPasswordPage() {
  const { t } = useTranslation();
  const { email, code } = useLocalSearchParams<{ email: string; code: string }>();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    if (!email || !code) {
      Alert.alert(
        t('auth.invalidAccess', { defaultValue: 'Invalid Access' }),
        t('auth.goThroughForgotPassword', { defaultValue: 'Please go through the forgot password process to reset your password.' }),
        [
          {
            text: t('common.ok', { defaultValue: 'OK' }),
            onPress: () => router.push('/authentication/verify-before-reset1')
          }
        ]
      );
    }
  }, [email, code]);

  const validatePassword = (password: string): string | null => {
    if (password.length < 6) {
      return t('validation.passwordTooShort', { defaultValue: 'Password must be at least 6 characters long' });
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return t('validation.passwordLowercase', { defaultValue: 'Password must contain at least one lowercase letter' });
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return t('validation.passwordUppercase', { defaultValue: 'Password must contain at least one uppercase letter' });
    }
    if (!/(?=.*\d)/.test(password)) {
      return t('validation.passwordNumber', { defaultValue: 'Password must contain at least one number' });
    }
    return null;
  };

  const handleResetPassword = async () => {
    if (!password.trim()) {
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('auth.enterNewPassword', { defaultValue: 'Please enter a new password' }));
      return;
    }

    if (!confirmPassword.trim()) {
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('auth.enterConfirmPassword', { defaultValue: 'Please confirm your password' }));
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('validation.passwordsDoNotMatch', { defaultValue: 'Passwords do not match' }));
      return;
    }

    const passwordError = validatePassword(password);
    if (passwordError) {
      Alert.alert(t('auth.invalidPassword', { defaultValue: 'Invalid Password' }), passwordError);
      return;
    }

    if (!email || !code) {
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('auth.invalidVerificationInfo', { defaultValue: 'Invalid verification information' }));
      return;
    }

    setLoading(true);
    try {
      const response = await apiService.resetPassword(email, code, password);
      
      if (response.success) {
        Alert.alert(
          t('auth.passwordResetSuccess', { defaultValue: 'Password Reset Successful' }),
          t('auth.passwordResetSuccessMessage', { defaultValue: 'Your password has been reset successfully. You can now login with your new password.' }),
          [
            {
              text: t('common.ok', { defaultValue: 'OK' }),
              onPress: () => router.push('/authentication/login')
            }
          ]
        );
      } else {
        Alert.alert(t('common.error', { defaultValue: 'Error' }), response.message || t('auth.failedToResetPassword', { defaultValue: 'Failed to reset password' }));
      }
    } catch (error) {
      console.error('Reset password error:', error);
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('auth.resetPasswordError', { defaultValue: 'Something went wrong. Please try again or request a new reset link.' }));
    } finally {
      setLoading(false);
    }
  };

  if (!email || !code) {
    return null;
  }

  return (
    <>
      <Stack.Screen options={{
        title: t('auth.resetPassword', { defaultValue: 'Reset Password' }),
        headerBackTitle: t('common.back', { defaultValue: 'Back' })
      }} />
      <Container alignSelf='center' alignItems="center" justifyContent="center" padding="$6" paddingTop={85} paddingBottom={85} style={{ width: '90%' }}>
        <Card padding="$6" width="100%" maxWidth={400} backgroundColor="$background">
          <YStack space="$4" alignItems="center">
            <YStack space="$2" alignItems="center">
              <Ionicons name="lock-closed" size={64} color="#67B329" />
              <H2 color="$color" textAlign="center">{t('auth.resetYourPassword', { defaultValue: 'Reset Your Password' })}</H2>
              <Text color="$gray10" textAlign="center" fontSize="$4">
                {t('auth.identityVerified', { defaultValue: 'Your identity has been verified. Enter your new password below.' })}
              </Text>
              <Text color="$gray10" textAlign="center" fontSize="$3">
                {t('auth.email', { defaultValue: 'Email' })}: {email}
              </Text>
            </YStack>

            <Separator />

            <YStack space="$3" width="100%">
              <YStack space="$2">
                <Label htmlFor="password">{t('auth.newPassword', { defaultValue: 'New Password' })}</Label>
                <XStack space="$2" alignItems="center">
                  <Ionicons name="lock-closed" size={20} color="#666" />
                  <Input
                    id="password"
                    flex={1}
                    value={password}
                    onChangeText={setPassword}
                    placeholder={t('auth.enterNewPassword', { defaultValue: 'Enter new password' })}
                    secureTextEntry={!showPassword}
                    editable={!loading}
                  />
                  <Button
                    size="$2"
                    variant="ghost"
                    onPress={() => setShowPassword(!showPassword)}
                    disabled={loading}
                  >
                    <Ionicons 
                      name={showPassword ? "eye-off" : "eye"} 
                      size={20} 
                      color="#666" 
                    />
                  </Button>
                </XStack>
              </YStack>

              <YStack space="$2">
                <Label htmlFor="confirmPassword">{t('auth.confirmNewPassword', { defaultValue: 'Confirm New Password' })}</Label>
                <XStack space="$2" alignItems="center">
                  <Ionicons name="lock-closed" size={20} color="#666" />
                  <Input
                    id="confirmPassword"
                    flex={1}
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    placeholder={t('auth.confirmNewPassword', { defaultValue: 'Confirm new password' })}
                    secureTextEntry={!showConfirmPassword}
                    editable={!loading}
                  />
                  <Button
                    size="$2"
                    variant="ghost"
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={loading}
                  >
                    <Ionicons 
                      name={showConfirmPassword ? "eye-off" : "eye"} 
                      size={20} 
                      color="#666" 
                    />
                  </Button>
                </XStack>
              </YStack>

              <YStack space="$2">
                <Text fontSize="$3" color="$gray10">
                  {t('auth.passwordRequirements', { defaultValue: 'Password requirements:' })}
                </Text>
                <Text fontSize="$2" color="$gray9">
                  {t('auth.passwordRequirementsList', { defaultValue: '• At least 6 characters long\n• Contains uppercase and lowercase letters\n• Contains at least one number' })}
                </Text>
              </YStack>

              <Button
                backgroundColor="#67B329"
                color="white"
                onPress={handleResetPassword}
                disabled={loading}
                opacity={loading ? 0.7 : 1}
              >
                {loading ? t('auth.resetting', { defaultValue: 'Resetting...' }) : t('auth.resetPassword', { defaultValue: 'Reset Password' })}
              </Button>

              <Button
                variant="outlined"
                onPress={() => router.push('/authentication/login')}
                disabled={loading}
              >
                {t('auth.backToLogin', { defaultValue: 'Back to Login' })}
              </Button>
            </YStack>
          </YStack>
        </Card>
      </Container>
    </>
  );
}
