import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types';

export const handleValidationErrors = (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: 'Validation failed',
      error: errors.array().map(err => err.msg).join(', '),
    });
    return;
  }
  
  next();
};

export const validateSignup = [
  // Basic Information
  body('firstName')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be between 1 and 50 characters'),

  body('lastName')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be between 1 and 50 characters'),

  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  // Contact & Security
  body('phoneNumber')
    .notEmpty()
    .matches(/^\+?[\d\s-()]+$/)
    .withMessage('Phone number is required and must be valid'),

  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),

  // Profile Setup
  body('username')
    .notEmpty()
    .trim()
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username is required, must be 3-30 characters, and contain only letters, numbers, and underscores'),

  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date'),

  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be male, female, or other'),

  // Address Information
  body('address')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Address is required and must be between 1 and 200 characters'),

  body('city')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('City is required and must be between 1 and 50 characters'),

  body('country')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Country is required and must be between 1 and 50 characters'),

  // Account Type
  body('role')
    .notEmpty()
    .isIn(['customer', 'supplier'])
    .withMessage('Role is required and must be either customer or supplier'),

  // Business Information (conditional for suppliers)
  body('storeName')
    .if(body('role').equals('supplier'))
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Store name is required for suppliers and must be between 1 and 100 characters'),

  body('businessType')
    .if(body('role').equals('supplier'))
    .notEmpty()
    .isIn(['restaurant', 'clothing', 'grocery', 'pharmacy', 'electronics', 'other'])
    .withMessage('Business type is required for suppliers'),

  body('openHours')
    .if(body('role').equals('supplier'))
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Open hours are required for suppliers'),

  // Location (optional)
  body('location')
    .optional()
    .isArray({ min: 2, max: 2 })
    .withMessage('Location must be an array of [longitude, latitude]'),

  body('location.*')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Location coordinates must be valid numbers'),

  // Preferences
  body('notifications')
    .optional()
    .isBoolean()
    .withMessage('Notifications must be a boolean value'),

  handleValidationErrors,
];

export const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  
  handleValidationErrors,
];

export const validateForgotPassword = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  handleValidationErrors,
];

export const validateResetPassword = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),

  body('code')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('Verification code must be a 6-digit number'),

  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),

  handleValidationErrors,
];

export const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, and one number'),
  
  handleValidationErrors,
];

export const validateUpdateProfile = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),

  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),

  body('phoneNumber')
    .optional()
    .matches(/^\+?[\d\s-()]+$/)
    .withMessage('Please provide a valid phone number'),

  // Support flat address field (mobile app format)
  body('address')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Address cannot exceed 200 characters'),

  // Support flat city field (mobile app format)
  body('city')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('City cannot exceed 50 characters'),

  // Support flat country field (mobile app format)
  body('country')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Country cannot exceed 50 characters'),

  // Support date of birth
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date'),

  // Support gender
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other', 'prefer-not-to-say'])
    .withMessage('Gender must be one of: male, female, other, prefer-not-to-say'),

  // Support nested address fields (web app format) - for backward compatibility
  body('address.street')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Street address cannot exceed 100 characters'),

  body('address.city')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('City cannot exceed 50 characters'),

  body('address.state')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('State cannot exceed 50 characters'),

  body('address.zipCode')
    .optional()
    .trim()
    .isLength({ max: 20 })
    .withMessage('Zip code cannot exceed 20 characters'),

  body('address.country')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Country cannot exceed 50 characters'),

  handleValidationErrors,
];
